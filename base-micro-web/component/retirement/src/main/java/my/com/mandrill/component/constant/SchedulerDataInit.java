package my.com.mandrill.component.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.service.SchedulerDataConfig;

@Getter
@AllArgsConstructor
public enum SchedulerDataInit implements SchedulerDataConfig {

	EPF_SAVING_CALCULATION(KafkaTopic.EPF_SAVING_CALCULATION, KafkaTopicConfig.GROUP, KafkaTopic.EPF_SAVING_CALCULATION,
			FIRST_DAY_EVERY_MONTH_12AM_KL_CRON);

	private final String destination;

	private final String jobGroup;

	private final String jobName;

	private final String cronExpression;

}
