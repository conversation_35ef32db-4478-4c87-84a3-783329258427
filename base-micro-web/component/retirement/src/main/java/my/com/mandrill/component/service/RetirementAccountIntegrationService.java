package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.RetirementAccount;
import my.com.mandrill.component.dto.request.RetirementAccountCreateRequest;
import my.com.mandrill.component.dto.request.RetirementAccountUpdateRequest;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;

public interface RetirementAccountIntegrationService {

	RetirementAccount processRetirementAccount(String userId, RetirementAccountCreateRequest request);

	RetirementAccount updateRetirementAccount(RetirementAccountUpdateRequest request, String accountId, String userId);

	NetWorthDTO calculateNetWorth(String userId);

	void deleteRetirementAccount(String accountId, String userId);

	void calculateEPFSavingMonthly();

}
