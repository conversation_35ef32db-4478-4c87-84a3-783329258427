package my.com.mandrill.component.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.constant.SchedulerDataInit;
import my.com.mandrill.utilities.general.config.CreateSchedulerConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;

@Configuration
@RequiredArgsConstructor
public class AutoCreateSchedulerConfig {

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final ObjectMapper objectMapper;

	@Bean
	public CreateSchedulerConfig<SchedulerDataInit> createSchedulerConfig() {
		return new CreateSchedulerConfig<>(kafkaTemplate, objectMapper, SchedulerDataInit.class);
	}

}
