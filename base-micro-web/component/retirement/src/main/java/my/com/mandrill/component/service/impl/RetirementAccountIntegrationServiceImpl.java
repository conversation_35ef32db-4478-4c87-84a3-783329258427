package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.RetirementAccount;
import my.com.mandrill.component.domain.RetirementAccountType;
import my.com.mandrill.component.dto.request.RetirementAccountCreateRequest;
import my.com.mandrill.component.dto.request.RetirementAccountUpdateRequest;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.feign.dto.NetWorthDTO;
import my.com.mandrill.utilities.feign.dto.SavingGoalDeletionDTO;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.constant.RetirementAccountTypeEnum;
import my.com.mandrill.utilities.general.constant.SavingGoalAccountType;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class RetirementAccountIntegrationServiceImpl implements RetirementAccountIntegrationService {

	private static final String EPF_ACCOUNT_TYPE_ID = "dd52a1d8-4e62-4c35-89bc-aed4c85acc80";

	private final RetirementAccountService retirementAccountService;

	private final RetirementAccountTypeService retirementAccountTypeService;

	private final RetirementProviderService retirementProviderService;

	private final ValidationService validationService;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private final JSONUtil jsonUtil;

	@Override
	public RetirementAccount processRetirementAccount(String userId, RetirementAccountCreateRequest request) {

		validationService.validateRetirementSavingsAmount(request.getSavingsAmount());
		validationService.validateRetirementAccountExists(userId, request.getAccountTypeId(), request.getProviderId());

		RetirementAccount retirementAccount = MapStructConverter.MAPPER.toRetirementAccount(request);
		retirementAccount.setUserId(userId);
		retirementAccount.setAccountType(retirementAccountTypeService.findById(request.getAccountTypeId()));
		if (!EPF_ACCOUNT_TYPE_ID.equals(request.getAccountTypeId())) {
			// there is no provider for EPF
			retirementAccount.setRetirementProvider(retirementProviderService.findById(request.getProviderId()));
		}

		return retirementAccountService.save(retirementAccount);
	}

	@Override
	public RetirementAccount updateRetirementAccount(RetirementAccountUpdateRequest request, String accountId,
			String userId) {
		validationService.validateRetirementSavingsAmount(request.getSavingsAmount());

		RetirementAccount existingRetirementAccount = retirementAccountService.findByIdAndUserId(accountId, userId);
		existingRetirementAccount.setSavingsAmount(request.getSavingsAmount());
		existingRetirementAccount.setMonthlyIncome(request.getMonthlyIncome());
		existingRetirementAccount.setEmployeeContributionPercentage(request.getEmployeeContributionPercentage());
		existingRetirementAccount.setEmployerContributionPercentage(request.getEmployerContributionPercentage());
		existingRetirementAccount.setMonthlyContribution(request.getMonthlyContribution());
		existingRetirementAccount.setLatestContributionDate(request.getLatestContributionDate());
		existingRetirementAccount.setLatestSavingCalculationDate(null);

		return retirementAccountService.save(existingRetirementAccount);

	}

	@Override
	public NetWorthDTO calculateNetWorth(String userId) {
		List<RetirementAccount> vehicles = retirementAccountService.findAllByUserId(userId);

		return NetWorthDTO.builder().liabilities(BigDecimal.ZERO).assets(
				vehicles.stream().map(RetirementAccount::getSavingsAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
				.build();
	}

	@Override
	public void deleteRetirementAccount(String accountId, String userId) {

		validationService.validateDeleteRetirementAccount(accountId, userId);

		retirementAccountService.deleteById(accountId);
		kafkaTemplate.send(KafkaTopic.SAVING_GOALS_DELETION, jsonUtil
				.convertToString(new SavingGoalDeletionDTO(userId, accountId, SavingGoalAccountType.RETIREMENT)));

	}

	@Override
	public void calculateEPFSavingMonthly() {
		Optional<RetirementAccountType> epfAccountTypeOptional = retirementAccountTypeService.findAll().stream()
				.filter(i -> RetirementAccountTypeEnum.EPF.name().equals(i.getCode())).findFirst();

		if (epfAccountTypeOptional.isEmpty()) {
			log.info("EPF Retirement Account Type is not found");
			return;
		}

		long offset = 0;
		while (true) {
			List<RetirementAccount> epfRetirementAccountList = retirementAccountService
					.findAllByAccountTypeIdAndMonthlyContributionNotNull(epfAccountTypeOptional.get().getId(), 1000,
							offset);

			if (epfRetirementAccountList.isEmpty()) {
				break;
			}

			offset = offset + epfRetirementAccountList.size();

			for (RetirementAccount epfRetirementAccount : epfRetirementAccountList) {
				long monthsBetween;
				if (epfRetirementAccount.getLatestSavingCalculationDate() != null) {
					monthsBetween = ChronoUnit.MONTHS.between(epfRetirementAccount.getLatestSavingCalculationDate(),
							LocalDate.now());
				}
				else {
					monthsBetween = ChronoUnit.MONTHS.between(epfRetirementAccount.getLatestContributionDate(),
							LocalDate.now());
				}

				if (monthsBetween > 0) {
					BigDecimal updatedSavingAmounts = epfRetirementAccount.getSavingsAmount().add((epfRetirementAccount
							.getMonthlyContribution().multiply(BigDecimal.valueOf(monthsBetween))));
					epfRetirementAccount.setSavingsAmount(updatedSavingAmounts);
					epfRetirementAccount.setLatestSavingCalculationDate(LocalDate.now());
					retirementAccountService.save(epfRetirementAccount);
				}
			}
		}
	}

}
