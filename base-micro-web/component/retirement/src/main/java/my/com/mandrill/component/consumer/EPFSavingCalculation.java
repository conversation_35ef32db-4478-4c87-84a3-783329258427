package my.com.mandrill.component.consumer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.service.RetirementAccountIntegrationService;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EPFSavingCalculation {

	private final RetirementAccountIntegrationService retirementAccountIntegrationService;

	@KafkaListener(topics = KafkaTopic.EPF_SAVING_CALCULATION, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopic.EPF_SAVING_CALCULATION)
	public void epfSavingCalculation(String message) {
		log.info("EPF Saving Calculation Begin {}", message);
		long start = System.currentTimeMillis();

		retirementAccountIntegrationService.calculateEPFSavingMonthly();
		log.info("EPF Saving Calculation Ended, Delay: {} ms", System.currentTimeMillis() - start);
	}

}