package my.com.mandrill.component.dto.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.utilities.general.constant.RSMHeaderStatusEnum;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateRsmHeaderStatusDTO {

	private String id;

	private String destination;

	private RSMHeaderStatusEnum status;

}
