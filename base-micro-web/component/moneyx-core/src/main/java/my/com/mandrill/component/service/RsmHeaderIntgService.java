package my.com.mandrill.component.service;

import jakarta.validation.Valid;
import my.com.mandrill.component.controller.PublicRsmHeaderController;
import my.com.mandrill.component.domain.RsmDetail;
import my.com.mandrill.component.domain.RsmHeader;
import my.com.mandrill.component.dto.model.UpdateRsmHeaderStatusDTO;
import my.com.mandrill.component.dto.request.BizCreateCommissionRequest;
import my.com.mandrill.component.dto.request.BizEditCommissionRequest;
import my.com.mandrill.component.dto.request.RSMHeaderSearchRequest;
import my.com.mandrill.component.dto.request.RsmHeaderRequest;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Set;

public interface RsmHeaderIntgService {

	RsmHeader create(RsmHeaderRequest request);

	RsmHeader update(String id, RsmHeaderRequest request);

	RsmHeaderDetailResponse mapRsmHeaderDetail(RsmHeader header, List<RsmDetail> details);

	// --- Biz ---
	Page<BizRSMCommissionListingResponse> getPaginatedCommissionListing(@NonNull Pageable pageable, String globalSearch,
			String productCategoryId, String partnerId, String productId, Boolean status);

	List<BizRSMCommissionDetailResponse> getCommissionListForExport(String globalSearch, String productCategoryId,
			String partnerId, String productId, Boolean status);

	BizRSMCommissionDetailResponse getBizCommissionDetailsById(@NonNull String id);

	void updateCommission(@NonNull String id, @NonNull BizEditCommissionRequest request);

	BizRSMCommissionDetailResponse createBizCommission(@Valid BizCreateCommissionRequest request);

	List<SearchRsmHeaderByProductResponse> searchRsmHeaders(RSMHeaderSearchRequest request);

	Set<String> getProductIdsWithActiveCommission(@NonNull SourceSystemEnum source);

	void UpdateRsmHeaderStatus(UpdateRsmHeaderStatusDTO updateRsmHeaderStatusDTO);

}