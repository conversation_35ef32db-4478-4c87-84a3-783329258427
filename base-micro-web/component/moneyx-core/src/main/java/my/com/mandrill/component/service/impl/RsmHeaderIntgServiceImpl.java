package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.component.constant.RunningNumberModule;
import my.com.mandrill.component.domain.RsmDetail;
import my.com.mandrill.component.domain.RsmHeader;
import my.com.mandrill.component.dto.model.BizRsmHeaderDetailDTO;
import my.com.mandrill.component.dto.model.RsmHeaderDetailDTO;
import my.com.mandrill.component.dto.model.RsmHeaderProductDTO;
import my.com.mandrill.component.dto.model.UpdateRsmHeaderStatusDTO;
import my.com.mandrill.component.dto.projection.BizRSMDetailProjection;
import my.com.mandrill.component.dto.projection.BizRSMHeaderWithDetailProjection;
import my.com.mandrill.component.dto.request.BizCreateCommissionRequest;
import my.com.mandrill.component.dto.request.BizEditCommissionRequest;
import my.com.mandrill.component.dto.request.RSMHeaderSearchRequest;
import my.com.mandrill.component.dto.request.RsmHeaderRequest;
import my.com.mandrill.component.dto.response.*;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.service.*;
import my.com.mandrill.utilities.general.constant.*;
import my.com.mandrill.utilities.general.dto.model.JobDTO;
import my.com.mandrill.utilities.general.util.JSONUtil;
import my.com.mandrill.utilities.general.util.RunningNumberUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class RsmHeaderIntgServiceImpl implements RsmHeaderIntgService {

	private final RsmHeaderService rsmHeaderService;

	private final RunningNumberUtil runningNumberUtil;

	private final RsmDetailService rsmDetailService;

	private final ValidationService validationService;

	private final JSONUtil jsonUtil;

	private final KafkaTemplate<String, String> kafkaTemplate;

	private static final String SCHEDULE_RSM_HEADER_STATUS_UPDATE = "schedule-header-status-update";

	private final SchedulerIntegrationService schedulerIntegrationService;

	@Override
	public RsmHeader create(RsmHeaderRequest request) {
		RsmHeader header = MapStructConverter.MAPPER.toRsmHeader(request);
		header.setCommissionId(
				runningNumberUtil.getLatestRunningNumberWithoutDate(RunningNumberModule.RSM_COMMISSION_ID.name()));
		header.setSource(SourceSystemEnum.MXAPP);
		if (Boolean.TRUE.equals(request.getIsDraft()))
			header.setStatus(RSMHeaderStatusEnum.DRAFT);
		else
			header.setStatus(RSMHeaderStatusEnum.SCHEDULED);

		List<RsmDetail> details = mapDetails(request.getDetail(), header);

		rsmHeaderService.save(header);
		rsmDetailService.saveAll(details);

		Pair<Instant, RSMHeaderStatusEnum> rsmHeaderStatusEnumPair = constructScheduler(header);

		if (rsmHeaderStatusEnumPair != null)
			callUpdateRsmHeaderScheduler(rsmHeaderStatusEnumPair, header.getId());

		return header;
	}

	private List<RsmDetail> mapDetails(Map<RSMFocalType, RsmHeaderDetailDTO> detailRequest, RsmHeader header) {
		List<RsmDetail> detailList = new ArrayList<>();

		for (Map.Entry<RSMFocalType, RsmHeaderDetailDTO> request : detailRequest.entrySet()) {
			RsmDetail detail = new RsmDetail();

			RsmHeaderDetailDTO detailDTO = request.getValue();
			detail.setHeader(header);
			detail.setFocalType(request.getKey());
			detail.setC2cRevenueDistribution(detailDTO.getC2cRevenueDist());
			detail.setB2b2cRevenueDistribution(detailDTO.getB2b2cRevenueDist());
			detail.setB2cRevenueDistribution(detailDTO.getB2cRevenueDist());
			detail.setNonReferralRevenueDistribution(detailDTO.getNonReferralRevenueDist());
			detail.setExternalB2bC2cNonReferralRevenueDistribution(detailDTO.getExternalB2bC2cNonReferralRevenueDist());
			detail.setExternalB2b2cRevenueDistribution(detailDTO.getExternalB2b2cRevenueDist());

			detailList.add(detail);
		}

		return detailList;
	}

	private boolean validateCurrentStatus(RsmHeader header) {
		RSMHeaderStatusEnum currentStatus = header.getStatus();

		return currentStatus != null && currentStatus != RSMHeaderStatusEnum.DRAFT
				&& currentStatus != RSMHeaderStatusEnum.EXPIRED;
	}

	private Pair<Instant, RSMHeaderStatusEnum> constructScheduler(RsmHeader header) {
		if (!validateCurrentStatus(header)) {
			return null;
		}

		RSMHeaderStatusEnum currentStatus = header.getStatus();

		return switch (currentStatus) {
			case SCHEDULED -> Pair.of(header.getExternalStartDate(), RSMHeaderStatusEnum.LIVE);
			case LIVE -> Pair.of(header.getExternalEndDate(), RSMHeaderStatusEnum.ENDED);
			case ENDED -> Pair.of(header.getEndDate(), RSMHeaderStatusEnum.EXPIRED);
			default -> null;
		};
	}

	private void callUpdateRsmHeaderScheduler(Pair<Instant, RSMHeaderStatusEnum> scheduleInfo, String headerId) {
		Instant scheduleTime = scheduleInfo.getLeft();
		RSMHeaderStatusEnum nextStatus = scheduleInfo.getRight();

		schedulerIntegrationService.ScheduleUpdateRsmHeaderStatus(scheduleTime,
				SCHEDULE_RSM_HEADER_STATUS_UPDATE + headerId, UpdateRsmHeaderStatusDTO.builder()
						.destination(KafkaTopicConfig.SET_RSM_HEADER_STATUS).id(headerId).status(nextStatus).build());
	}

	@Override
	public RsmHeader update(String id, RsmHeaderRequest request) {
		RsmHeader header = rsmHeaderService.findById(id);

		List<RsmDetail> details = rsmDetailService.findByHeader(List.of(id));
		if (details.isEmpty())
			throw ExceptionPredicate.rsmDetailNotFoundByHeaderId(id).get();

		validationService.validateCompulsoryFields(request);
		validationService.validateRsmHeader(request);
		validationService.validateDuplicateRsmHeader(request);

		RsmHeaderProductDTO product = validationService.validateProduct(request.getProduct());
		request.setProduct(product);

		RsmHeader updateHeader = MapStructConverter.MAPPER.toRsmHeader(request);
		updateHeader.setId(header.getId());
		updateHeader.setCommissionId(header.getCommissionId());
		updateHeader.setSource(header.getSource());

		List<RsmDetail> updateDetails = mapDetails(request.getDetail(), updateHeader, details);

		rsmHeaderService.save(updateHeader);
		rsmDetailService.saveAll(updateDetails);

		Pair<Instant, RSMHeaderStatusEnum> rsmHeaderStatusEnumPair = constructScheduler(header);
		if (rsmHeaderStatusEnumPair != null)
			callUpdateRsmHeaderScheduler(rsmHeaderStatusEnumPair, header.getId());

		return updateHeader;

	}

	private List<RsmDetail> mapDetails(Map<RSMFocalType, RsmHeaderDetailDTO> detailRequest, RsmHeader header,
			List<RsmDetail> details) {
		for (RsmDetail existingDetail : details) {
			RsmHeaderDetailDTO request = detailRequest.get(existingDetail.getFocalType());

			existingDetail.setHeader(header);
			existingDetail.setC2cRevenueDistribution(request.getC2cRevenueDist());
			existingDetail.setB2b2cRevenueDistribution(request.getB2b2cRevenueDist());
			existingDetail.setB2cRevenueDistribution(request.getB2cRevenueDist());
			existingDetail.setNonReferralRevenueDistribution(request.getNonReferralRevenueDist());
			existingDetail
					.setExternalB2bC2cNonReferralRevenueDistribution(request.getExternalB2bC2cNonReferralRevenueDist());
			existingDetail.setExternalB2b2cRevenueDistribution(request.getExternalB2b2cRevenueDist());
		}

		return details;
	}

	@Override
	public RsmHeaderDetailResponse mapRsmHeaderDetail(RsmHeader header, List<RsmDetail> details) {
		RsmHeaderDetailResponse response = new RsmHeaderDetailResponse();
		response.setCommissionId(header.getCommissionId());
		response.setRewardName(header.getRewardName());
		response.setIsActive(header.getIsActive());
		response.setCommissionType(header.getCommissionType());
		response.setRemarks(header.getRemarks());
		response.setFrequency(header.getFrequency());
		response.setStartDate(header.getStartDate().atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).toLocalDate());
		response.setEndDate(header.getEndDate().atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).toLocalDate());
		response.setStatus(header.getStatus());
		response.setExternalEndDate(
				header.getExternalEndDate().atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).toLocalDate());
		response.setExternalStartDate(
				header.getExternalStartDate().atZone(ZoneId.of(TimeConstant.DEFAULT_TIMEZONE)).toLocalDate());
		response.setExternalRevenue(header.getExternalRevenue());
		response.setExternalRewardName(header.getExternalRewardName());

		RsmHeaderProductDTO productDTO = MapStructConverter.MAPPER.toRsmHeaderProductDTO(header);
		response.setProduct(productDTO);
		response.setDetail(mapRsmDetails(details));

		return response;
	}

	// --- Biz ---

	@Override
	public Page<BizRSMCommissionListingResponse> getPaginatedCommissionListing(@NonNull Pageable pageable,
			String globalSearch, String productCategoryId, String partnerId, String productId, Boolean status) {
		return rsmHeaderService.getPaginatedCommissionListing(pageable, globalSearch, productCategoryId, partnerId,
				productId, status);
	}

	@Override
	public List<BizRSMCommissionDetailResponse> getCommissionListForExport(String globalSearch,
			String productCategoryId, String partnerId, String productId, Boolean status) {
		List<BizRSMHeaderWithDetailProjection> commissionListWithDetailProjection = rsmHeaderService
				.getCommissionListWithDetail(globalSearch, productCategoryId, partnerId, productId, status);

		return commissionListWithDetailProjection.stream().map(projection -> {
			BizRSMCommissionDetailResponse detailResponse = MapStructConverter.MAPPER
					.toBizRSMCommissionDetailResponse(projection);

			Map<RSMFocalType, BizRsmHeaderDetailDTO> detailMap = projection.getDetails().stream()
					.collect(Collectors.toMap(BizRSMDetailProjection::getFocalType,
							v -> BizRsmHeaderDetailDTO.builder()
									.b2bRsmRevenueDistribution(v.getB2bRevenueDistribution())
									.nonReferralRsmRevenueDistribution(v.getNonReferralRevenueDistribution()).build()));

			detailResponse.setDetail(detailMap);

			return detailResponse;
		}).toList();
	}

	@Override
	public BizRSMCommissionDetailResponse getBizCommissionDetailsById(@NonNull String id) {
		RsmHeader header = rsmHeaderService.findById(id);

		List<RsmDetail> details = rsmDetailService.findByHeader(List.of(id));
		if (details.isEmpty())
			throw ExceptionPredicate.rsmDetailNotFoundByHeaderId(id).get();

		BizRSMCommissionDetailResponse response = MapStructConverter.MAPPER.toBizRSMCommissionDetailResponse(header);

		Map<RSMFocalType, BizRsmHeaderDetailDTO> detailMap = details.stream()
				.collect(Collectors.toMap(RsmDetail::getFocalType,
						v -> BizRsmHeaderDetailDTO.builder().b2bRsmRevenueDistribution(v.getB2bRevenueDistribution())
								.nonReferralRsmRevenueDistribution(v.getNonReferralRevenueDistribution()).build()));
		response.setDetail(detailMap);

		return response;
	}

	@Override
	public void updateCommission(@NonNull String id, @NonNull BizEditCommissionRequest request) {
		RsmHeader header = rsmHeaderService.findById(id);

		if (request.getActive() != null)
			header.setIsActive(request.getActive());

		String remarks = request.getRemarks();
		if (remarks != null)
			remarks = remarks.trim();

		header.setRemarks(remarks);

		rsmHeaderService.save(header);
	}

	@Override
	public BizRSMCommissionDetailResponse createBizCommission(@NonNull BizCreateCommissionRequest request) {
		validationService.validateBizCommission(request);

		RsmHeader header = MapStructConverter.MAPPER.toRsmHeader(request);
		String runningNumber = runningNumberUtil
				.getLatestRunningNumberWithoutDate(RunningNumberModule.RSM_COMMISSION_ID.name());

		header.setCommissionId(runningNumber);
		header.setSource(SourceSystemEnum.MXBIZ);

		List<RsmDetail> details = request.getBreakdown().entrySet().stream().map(entry -> {
			RsmDetail detail = new RsmDetail();
			detail.setHeader(header);
			detail.setFocalType(entry.getKey());
			detail.setB2bRevenueDistribution(entry.getValue().getB2bRsmRevenueDistribution());
			detail.setNonReferralRevenueDistribution(entry.getValue().getNonReferralRsmRevenueDistribution());
			return detail;
		}).toList();

		rsmHeaderService.create(header, details);

		return this.getBizCommissionDetailsById(header.getId());
	}

	@Override
	public List<SearchRsmHeaderByProductResponse> searchRsmHeaders(RSMHeaderSearchRequest query) {
		List<SearchRsmHeaderByProductResponse> response = rsmHeaderService.findAll(query).stream()
				.map(MapStructConverter.MAPPER::toSearchRsmHeaderByProductResponse).collect(Collectors.toList());

		for (SearchRsmHeaderByProductResponse header : response) {
			List<RsmDetail> details = rsmDetailService.findByHeader(List.of(header.getId()));

			if (details.isEmpty()) {
				throw ExceptionPredicate.rsmDetailNotFoundByHeaderId(header.getId()).get();
			}
			header.setRsmDetail(mapRsmDetails(details));
		}

		return response;
	}

	@Override
	public Set<String> getProductIdsWithActiveCommission(@NonNull SourceSystemEnum source) {
		return rsmHeaderService.getProductIdsWithActiveCommission(source);
	}

	private Map<RSMFocalType, RsmHeaderDetailDTO> mapRsmDetails(List<RsmDetail> details) {
		Map<RSMFocalType, RsmHeaderDetailDTO> detailMap = new EnumMap<>(RSMFocalType.class);
		for (RsmDetail detail : details) {

			RsmHeaderDetailDTO detailDTO = new RsmHeaderDetailDTO();
			detailDTO.setB2b2cRevenueDist(detail.getB2b2cRevenueDistribution());
			detailDTO.setB2cRevenueDist(detail.getB2cRevenueDistribution());
			detailDTO.setC2cRevenueDist(detail.getC2cRevenueDistribution());
			detailDTO.setNonReferralRevenueDist(detail.getNonReferralRevenueDistribution());

			detailMap.put(detail.getFocalType(), detailDTO);
		}
		return detailMap;
	}

	@Override
	public void UpdateRsmHeaderStatus(UpdateRsmHeaderStatusDTO updateRsmHeaderStatusDTO) {
		RsmHeader header = rsmHeaderService.findById(updateRsmHeaderStatusDTO.getId());
		header.setStatus(updateRsmHeaderStatusDTO.getStatus());
		rsmHeaderService.save(header);

		Pair<Instant, RSMHeaderStatusEnum> rsmHeaderStatusEnumPair = constructScheduler(header);
		if (rsmHeaderStatusEnumPair != null)
			callUpdateRsmHeaderScheduler(rsmHeaderStatusEnumPair, header.getId());
	}

}