package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.ChartOfAccountType;
import my.com.mandrill.component.constant.PointTransactionStatus;
import my.com.mandrill.component.constant.PointTransactionType;
import my.com.mandrill.component.constant.RunningNumberModule;
import my.com.mandrill.component.domain.ChartOfAccount;
import my.com.mandrill.component.domain.PointAllocation;
import my.com.mandrill.component.domain.PointTransaction;
import my.com.mandrill.component.dto.projection.PointEarningAllocationProjection;
import my.com.mandrill.component.service.ChartOfAccountService;
import my.com.mandrill.component.service.PointAllocationService;
import my.com.mandrill.component.service.**********************;
import my.com.mandrill.component.service.PointTransactionService;
import my.com.mandrill.utilities.general.util.RunningNumberUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class **********************Impl implements ********************** {

	private final ChartOfAccountService chartOfAccountService;

	private final PointAllocationService pointAllocationService;

	private final PointTransactionService pointTransactionService;

	private final RunningNumberUtil runningNumberUtil;

	@Transactional
	@Override
	public void processExpiredPoints() {

		List<PointEarningAllocationProjection> expiredPointEarnings = getExpiredPointEarnings();
		if (CollectionUtils.isEmpty(expiredPointEarnings)) {
			log.info("no expired points found to process");
			return;
		}

		ChartOfAccount pointExpiryCoa = getPointExpiryCoa();
		processExpiredPoints(expiredPointEarnings, pointExpiryCoa);

	}

	private List<PointEarningAllocationProjection> getExpiredPointEarnings() {
		return pointAllocationService.findAllExpiredPointEarnings();
	}

	private ChartOfAccount getPointExpiryCoa() {
		return chartOfAccountService.findByAccountCode(ChartOfAccountType.RSM_POINT_EXPIRY.getCode());
	}

	private void processExpiredPoints(List<PointEarningAllocationProjection> expiredPointEarnings,
			ChartOfAccount pointExpiryCoa) {
		List<PointAllocation> expiryAllocations = new ArrayList<>();

		expiredPointEarnings.forEach(expiredPointEarning -> {
			log.info("processing expired points for user {}, remaining points: {}", expiredPointEarning.getUserId(),
					expiredPointEarning.getRemainingPoints());

			PointTransaction pointExpiryTransaction = createPointExpiryTransaction(expiredPointEarning, pointExpiryCoa);
			pointTransactionService.save(pointExpiryTransaction);

			PointAllocation expiryAllocation = createPointExpiryAllocation(expiredPointEarning.getTransactionId(),
					pointExpiryTransaction.getId(), expiredPointEarning.getRemainingPoints());
			expiryAllocations.add(expiryAllocation);

		});

		pointAllocationService.saveAll(expiryAllocations);
		log.info("done processed {} point expiry records", expiredPointEarnings.size());
	}

	private PointTransaction createPointExpiryTransaction(PointEarningAllocationProjection expiredPointEarning,
			ChartOfAccount chartOfAccount) {

		PointTransaction pointTransaction = new PointTransaction();
		pointTransaction.setUserId(expiredPointEarning.getUserId());
		pointTransaction.setSource(expiredPointEarning.getSource());
		pointTransaction.setStatus(PointTransactionStatus.SUCCESS);
		pointTransaction.setPointAmount(expiredPointEarning.getRemainingPoints());
		pointTransaction.setRefNo(runningNumberUtil.getLatestRunningNumber(RunningNumberModule.TRX_ID.name()));
		pointTransaction.setType(PointTransactionType.POINT_EXPIRY);
		pointTransaction.setName(PointTransactionType.POINT_EXPIRY.getName());
		pointTransaction.setChartOfAccount(chartOfAccount);
		pointTransaction.setApprovedDate(Instant.now());

		return pointTransaction;
	}

	private PointAllocation createPointExpiryAllocation(String earningTxId, String expiryTxId, BigDecimal toAllocate) {

		PointAllocation pointAllocation = new PointAllocation();
		pointAllocation.setOriginTxId(earningTxId);
		pointAllocation.setDestTxId(expiryTxId);
		pointAllocation.setPointsAllocated(toAllocate);

		return pointAllocation;
	}

}
