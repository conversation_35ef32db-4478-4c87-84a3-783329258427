package my.com.mandrill.component.dto.request;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.RSMCommissionType;
import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.component.dto.model.RsmHeaderDetailDTO;
import my.com.mandrill.component.dto.model.RsmHeaderProductDTO;
import my.com.mandrill.utilities.general.constant.RSMFrequencyEnum;
import my.com.mandrill.utilities.general.constant.RSMHeaderStatusEnum;
import my.com.mandrill.utilities.general.constant.RSMStatus;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RsmHeaderRequest {

	private String rewardName;

	private RSMCommissionType commissionType;

	@Builder.Default
	private Boolean isActive = Boolean.TRUE;

	private RsmHeaderProductDTO product;

	@JsonSerialize
	@JsonDeserialize
	private Map<RSMFocalType, RsmHeaderDetailDTO> detail;

	@Size(max = 150)
	private String remarks;

	private RSMFrequencyEnum frequency;

	private Instant startDate;

	private Instant endDate;

	private BigDecimal externalRevenue;

	private String externalRewardName;

	private Instant externalStartDate;

	private Instant externalEndDate;

	private Boolean isDraft;

}
