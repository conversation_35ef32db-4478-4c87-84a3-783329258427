package my.com.mandrill.component.controller.admin;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.MapStructConverter;
import my.com.mandrill.component.domain.RsmDetail;
import my.com.mandrill.component.domain.RsmHeader;
import my.com.mandrill.component.dto.model.RsmHeaderProductDTO;
import my.com.mandrill.component.dto.request.RSMHeaderSearchRequest;
import my.com.mandrill.component.dto.request.RsmHeaderRequest;
import my.com.mandrill.component.dto.response.RsmHeaderDetailResponse;
import my.com.mandrill.component.dto.response.RsmHeaderResponse;
import my.com.mandrill.component.dto.response.SearchRsmHeaderByProductResponse;
import my.com.mandrill.component.exception.ExceptionPredicate;
import my.com.mandrill.component.service.RsmDetailService;
import my.com.mandrill.component.service.RsmHeaderIntgService;
import my.com.mandrill.component.service.RsmHeaderService;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.core.annotation.ServiceToServiceAccess;
import my.com.mandrill.utilities.general.constant.RSMFrequencyEnum;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/rsm-commission-header")
public class AdminRsmHeaderController {

	private final ValidationService validationService;

	private final RsmHeaderIntgService rsmHeaderIntgService;

	private final RsmHeaderService rsmHeaderService;

	private final RsmDetailService rsmDetailService;

	@PostMapping
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_HEADER_CREATE)")
	public ResponseEntity<RsmHeader> create(@RequestBody @Valid RsmHeaderRequest request) {
		validationService.validateCompulsoryFields(request);
		validationService.validateRsmHeader(request);
		validationService.validateDuplicateRsmHeader(request);

		RsmHeaderProductDTO product = validationService.validateProduct(request.getProduct());
		request.setProduct(product);

		RsmHeader header = rsmHeaderIntgService.create(request);

		return ResponseEntity.ok(header);
	}

	@PutMapping("{id}/status/{active}")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_HEADER_UPDATE)")
	public void updateStatus(@PathVariable String id, @PathVariable boolean active) {
		RsmHeader header = rsmHeaderService.findById(id);
		header.setIsActive(active);

		rsmHeaderService.save(header);
	}

	@PutMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_HEADER_UPDATE)")
	public ResponseEntity<RsmHeader> update(@PathVariable String id, @Valid @RequestBody RsmHeaderRequest request) {
		return ResponseEntity.ok(rsmHeaderIntgService.update(id, request));
	}

	@GetMapping
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_HEADER_READ)")
	public ResponseEntity<Page<RsmHeaderResponse>> findAllWithPagination(@RequestParam(required = false) String search,
			@RequestParam(required = false) Boolean isActive, @RequestParam(required = false) String productCategoryId,
			@RequestParam(required = false) String productTypeId, @RequestParam(required = false) String providerId,
			@RequestParam(required = false) RSMFrequencyEnum frequency,
			@RequestParam(required = false) LocalDate startDate, @RequestParam(required = false) LocalDate endDate,
			@RequestParam(required = false) Boolean isInternal, Pageable pageable) {
		Page<RsmHeader> headers = rsmHeaderService.findAll(
				RSMHeaderSearchRequest.builder().search(search).isActive(isActive).productCategoryId(productCategoryId)
						.productTypeId(productTypeId).providerId(providerId).source(SourceSystemEnum.MXAPP)
						.frequency(frequency).startDate(startDate).endDate(endDate).isInternal(isInternal).build(),
				pageable);
		return ResponseEntity.ok(headers.map(MapStructConverter.MAPPER::toRsmHeaderResponse));
	}

	@GetMapping("{id}")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_HEADER_READ)")
	public ResponseEntity<RsmHeaderDetailResponse> findById(@PathVariable String id) {
		RsmHeader header = rsmHeaderService.findById(id);

		List<RsmDetail> details = rsmDetailService.findByHeader(List.of(id));
		if (details.isEmpty())
			throw ExceptionPredicate.rsmDetailNotFoundByHeaderId(id).get();

		RsmHeaderDetailResponse response = rsmHeaderIntgService.mapRsmHeaderDetail(header, details);
		response.setId(id);

		return ResponseEntity.ok(response);
	}

	@GetMapping("/search")
	@PreAuthorize("hasAuthority(@authorityPermission.RSM_HEADER_READ)")
	public ResponseEntity<List<SearchRsmHeaderByProductResponse>> searchRsmHeader(
			@RequestParam(required = false) Boolean isActive, @RequestParam(required = false) String productCategoryId,
			@RequestParam(required = false) String productId, @RequestParam(required = false) String providerId,
			@RequestParam(required = false) RSMFrequencyEnum frequency,
			@RequestParam(required = false) LocalDate startDate, @RequestParam(required = false) LocalDate endDate,
			@RequestParam(required = false) Boolean isInternal) {
		return ResponseEntity.ok(rsmHeaderIntgService.searchRsmHeaders(RSMHeaderSearchRequest.builder()
				.isActive(isActive).productId(productId).productCategoryId(productCategoryId).providerId(providerId)
				.isInternal(isInternal).startDate(startDate).endDate(endDate).source(SourceSystemEnum.MXAPP).build()));
	}

	@ServiceToServiceAccess
	@GetMapping("/private/products/types")
	public ResponseEntity<List<String>> getRsmActiveProductTypes() {
		return ResponseEntity.ok(rsmHeaderService.findProductTypeIdsWithActiveCommission(SourceSystemEnum.MXAPP));
	}

	@ServiceToServiceAccess
	@GetMapping("/private/products")
	public ResponseEntity<Set<String>> getRsmActiveProducts() {
		return ResponseEntity.ok(rsmHeaderService.getProductIdsWithActiveCommission(SourceSystemEnum.MXAPP));
	}

	@ServiceToServiceAccess
	@GetMapping("/private/products/by-type")
	public ResponseEntity<Set<String>> getRsmActiveProducts(@RequestParam(required = false) String productTypeName) {
		return ResponseEntity
				.ok(rsmHeaderService.getProductIdsWithActiveCommission(SourceSystemEnum.MXAPP, productTypeName));
	}

	@ServiceToServiceAccess
	@GetMapping("/private/providers/by-type")
	public ResponseEntity<Set<String>> getRsmActiveProviders(
			@RequestParam(required = false) List<String> productTypeNames) {
		return ResponseEntity
				.ok(rsmHeaderService.findAllProviderIdsWithActiveCommission(SourceSystemEnum.MXAPP, productTypeNames));
	}

}
