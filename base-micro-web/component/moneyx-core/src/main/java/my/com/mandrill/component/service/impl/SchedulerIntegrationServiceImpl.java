package my.com.mandrill.component.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.dto.model.UpdateRsmHeaderStatusDTO;
import my.com.mandrill.component.service.SchedulerIntegrationService;
import my.com.mandrill.utilities.general.constant.SchedulerConstant;
import my.com.mandrill.utilities.general.constant.SchedulerType;
import my.com.mandrill.utilities.general.dto.model.JobDTO;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class SchedulerIntegrationServiceImpl implements SchedulerIntegrationService {

	private final JSONUtil jsonUtil;

	private final KafkaTemplate<String, String> kafkaTemplate;

	public void ScheduleUpdateRsmHeaderStatus(Instant scheduleTime, String jobName, UpdateRsmHeaderStatusDTO dto) {
		Map<String, String> data = jsonUtil.convertValue(dto, new TypeReference<>() {
		});

		JobDTO jobDTO = JobDTO.builder().jobGroup(KafkaTopicConfig.GROUP).jobName(jobName)
				.schedulerType(SchedulerType.ONE_TIME).scheduleTime(scheduleTime).data(data).build();

		kafkaTemplate.send(SchedulerConstant.UPSERT_SCHEDULER_TOPIC, jobDTO.getJobGroup(),
				jsonUtil.convertToString(jobDTO));
	}

}
