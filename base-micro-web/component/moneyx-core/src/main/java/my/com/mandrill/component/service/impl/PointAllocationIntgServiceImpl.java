package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.domain.PointAllocation;
import my.com.mandrill.component.dto.projection.PointEarningAllocationProjection;
import my.com.mandrill.component.service.PointAllocationIntgService;
import my.com.mandrill.component.service.PointAllocationService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class PointAllocationIntgServiceImpl implements PointAllocationIntgService {

	private final PointAllocationService pointAllocationService;

	@Override
	public void processPointAllocation(String userId, BigDecimal totalPointsToAllocate, String withdrawalTxId) {

		List<PointAllocation> pointAllocations = new ArrayList<>();
		BigDecimal remainingPointsToAllocate = totalPointsToAllocate;

		// FIFO allocation, consume oldest points first
		List<PointEarningAllocationProjection> availablePointEarnings = pointAllocationService
				.findAvailablePointEarnings(userId);
		for (PointEarningAllocationProjection availablePointEarning : availablePointEarnings) {

			if (remainingPointsToAllocate.compareTo(BigDecimal.ZERO) == 0) {
				// no more points to allocate
				break;
			}

			// determine how many points to allocate from this earning
			// if we need 100 points but only 75 are available, then allocate 75
			// if we need 50 points and 100 are available, then allocate 50
			BigDecimal toAllocate = remainingPointsToAllocate.min(availablePointEarning.getRemainingPoints());

			pointAllocations
					.add(createPointAllocation(availablePointEarning.getTransactionId(), withdrawalTxId, toAllocate));

			remainingPointsToAllocate = remainingPointsToAllocate.subtract(toAllocate);

		}

		pointAllocationService.saveAll(pointAllocations);

	}

	private PointAllocation createPointAllocation(String earningTxId, String withdrawalTxId, BigDecimal toAllocate) {

		PointAllocation pointAllocation = new PointAllocation();
		pointAllocation.setOriginTxId(earningTxId);
		pointAllocation.setDestTxId(withdrawalTxId);
		pointAllocation.setPointsAllocated(toAllocate);

		return pointAllocation;
	}

}
