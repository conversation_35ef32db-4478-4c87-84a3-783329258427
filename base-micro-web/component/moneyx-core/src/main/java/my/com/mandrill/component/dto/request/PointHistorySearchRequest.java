package my.com.mandrill.component.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PointHistorySearchRequest {

	private String search;

	private SourceSystemEnum source;

	private String rsmScenario;

	private RSMFocalType rsmFocalType;

	private String accountId;

	private Instant startDate;

	private Instant endDate;

}
