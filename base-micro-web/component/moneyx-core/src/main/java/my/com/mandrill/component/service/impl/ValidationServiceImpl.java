package my.com.mandrill.component.service.impl;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.RSMCommissionType;
import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.component.domain.RsmHeader;
import my.com.mandrill.component.dto.model.RsmHeaderDetailDTO;
import my.com.mandrill.component.dto.model.RsmHeaderProductDTO;
import my.com.mandrill.component.dto.request.BizCreateCommissionRequest;
import my.com.mandrill.component.dto.request.RsmHeaderRequest;
import my.com.mandrill.component.exception.ErrorCodeEnum;
import my.com.mandrill.component.repository.RsmHeaderRepository;
import my.com.mandrill.component.service.ValidationService;
import my.com.mandrill.utilities.feign.client.AiFeignClient;
import my.com.mandrill.utilities.general.constant.RSMHeaderStatusEnum;
import my.com.mandrill.utilities.general.exception.BusinessException;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class ValidationServiceImpl implements ValidationService {

	private static final BigDecimal RSM_REVENUE_MIN = BigDecimal.ZERO;

	private static final BigDecimal RSM_REVENUE_MAX = BigDecimal.valueOf(1000000);

	private static final BigDecimal RSM_REVENUE_DISTRIBUTION_TOTAL_PERCENTAGE = BigDecimal.valueOf(100);

	private final AiFeignClient aiFeignClient;

	private final RsmHeaderRepository headerRepository;

	@Override
	public void validateRsmHeader(RsmHeaderRequest request) {

		RsmHeaderProductDTO product = request.getProduct();
		Map<RSMFocalType, RsmHeaderDetailDTO> details = request.getDetail();

		validateRevenueRange(product.getProductRevenue(), request.getExternalRevenue());
		validateSameRevenueSharingDistribution(details.get(RSMFocalType.REFEREE));

		// Validate Revenue Distribution not negative and sum up
		BigDecimal totalC2cRevenueDist = BigDecimal.ZERO;
		BigDecimal totalB2cRevenueDist = BigDecimal.ZERO;
		BigDecimal totalB2b2cRevenueDist = BigDecimal.ZERO;
		BigDecimal totalNonReferralRevenueDist = BigDecimal.ZERO;

		for (Map.Entry<RSMFocalType, RsmHeaderDetailDTO> detail : details.entrySet()) {

			BigDecimal c2cRevenueDist = detail.getValue().getC2cRevenueDist();
			BigDecimal b2cRevenueDist = detail.getValue().getB2cRevenueDist();
			BigDecimal b2b2cRevenueDist = detail.getValue().getB2b2cRevenueDist();
			BigDecimal nonReferralRevenueDist = detail.getValue().getNonReferralRevenueDist();

			// Throw error if the value is negative
			if (c2cRevenueDist.compareTo(BigDecimal.ZERO) < 0 || b2cRevenueDist.compareTo(BigDecimal.ZERO) < 0
					|| b2b2cRevenueDist.compareTo(BigDecimal.ZERO) < 0
					|| nonReferralRevenueDist.compareTo(BigDecimal.ZERO) < 0) {
				throw new BusinessException(ErrorCodeEnum.INVALID_REVENUE_DISTRIBUTION);
			}

			// Add the value to the total
			totalC2cRevenueDist = totalC2cRevenueDist.add(c2cRevenueDist);
			totalB2cRevenueDist = totalB2cRevenueDist.add(b2cRevenueDist);
			totalB2b2cRevenueDist = totalB2b2cRevenueDist.add(b2b2cRevenueDist);
			totalNonReferralRevenueDist = totalNonReferralRevenueDist.add(nonReferralRevenueDist);
		}

		// For AMOUNT, verify total Revenue Distribution = Revenue
		if (request.getCommissionType() == RSMCommissionType.AMOUNT) {

			BigDecimal revenue = product.getProductRevenue();

			if (totalC2cRevenueDist.compareTo(revenue) != 0 || totalB2cRevenueDist.compareTo(revenue) != 0
					|| totalB2b2cRevenueDist.compareTo(revenue) != 0
					|| totalNonReferralRevenueDist.compareTo(revenue) != 0) {
				throw new BusinessException(ErrorCodeEnum.REVENUE_DISTRIBUTION_NOT_TALLY_WITH_REVENUE);
			}
		}

		// For PERCENTAGE, verify total Revenue Distribution = 100%
		if (request.getCommissionType() == RSMCommissionType.PERCENTAGE
				&& (totalC2cRevenueDist.compareTo(RSM_REVENUE_DISTRIBUTION_TOTAL_PERCENTAGE) != 0
						|| totalB2cRevenueDist.compareTo(RSM_REVENUE_DISTRIBUTION_TOTAL_PERCENTAGE) != 0
						|| totalB2b2cRevenueDist.compareTo(RSM_REVENUE_DISTRIBUTION_TOTAL_PERCENTAGE) != 0
						|| totalNonReferralRevenueDist.compareTo(RSM_REVENUE_DISTRIBUTION_TOTAL_PERCENTAGE) != 0)) {
			throw new BusinessException(ErrorCodeEnum.REVENUE_DISTRIBUTION_PERCENTAGE_NOT_TALLY);
		}
	}

	private void validateSameRevenueSharingDistribution(RsmHeaderDetailDTO details) {
		boolean refereeRevenueDist = Stream.of(details.getC2cRevenueDist(), details.getB2cRevenueDist(),
				details.getB2b2cRevenueDist(), details.getNonReferralRevenueDist()).distinct().count() == 1;

		if (!refereeRevenueDist) {
			throw new BusinessException(ErrorCodeEnum.RSM_COMMISSION_REFEREE_REVENUE_DISTRIBUTION_SHARING_DIFFERENT);
		}
	}

	@Override
	public void validateBizCommission(BizCreateCommissionRequest request) {
		BigDecimal totalRevenue = request.getProductRevenue();
		this.validateRevenueRange(request.getCommissionType(), totalRevenue);

		BigDecimal refereeB2bDistribution = ObjectUtils.defaultIfNull(
				request.getBreakdown().get(RSMFocalType.REFEREE).getB2bRsmRevenueDistribution(), BigDecimal.ZERO);
		BigDecimal refereeNonReferralB2bDistribution = ObjectUtils.defaultIfNull(
				request.getBreakdown().get(RSMFocalType.REFEREE).getNonReferralRsmRevenueDistribution(),
				BigDecimal.ZERO);

		if (refereeB2bDistribution.compareTo(refereeNonReferralB2bDistribution) != 0) {
			throw new BusinessException(
					ErrorCodeEnum.RSM_COMMISSION_REFEREE_B2B_AND_NON_REFERRAL_DISTRIBUTION_NOT_EQUAL);
		}

		BigDecimal totalB2b = request.getBreakdown().values().stream()
				.map(v -> v == null ? BigDecimal.ZERO : v.getB2bRsmRevenueDistribution())
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		BigDecimal totalNonReferral = request.getBreakdown().values().stream()
				.map(v -> v == null ? BigDecimal.ZERO : v.getNonReferralRsmRevenueDistribution())
				.reduce(BigDecimal.ZERO, BigDecimal::add);

		switch (request.getCommissionType()) {
			case AMOUNT -> {
				if (totalB2b.compareTo(totalRevenue) != 0 || totalNonReferral.compareTo(totalRevenue) != 0) {
					throw new BusinessException(ErrorCodeEnum.REVENUE_DISTRIBUTION_NOT_TALLY_WITH_REVENUE);
				}
			}
			case PERCENTAGE -> {
				if (totalB2b.compareTo(RSM_REVENUE_DISTRIBUTION_TOTAL_PERCENTAGE) != 0
						|| totalNonReferral.compareTo(RSM_REVENUE_DISTRIBUTION_TOTAL_PERCENTAGE) != 0) {
					throw new BusinessException(ErrorCodeEnum.REVENUE_DISTRIBUTION_PERCENTAGE_NOT_TALLY);
				}
			}
		}

		if (headerRepository.existsByProductIdAndRewardNameIgnoreCase(request.getProductId(),
				request.getRewardName())) {
			throw new BusinessException(ErrorCodeEnum.RSM_COMMISSION_EXISTS);
		}
	}

	private void validateRevenueRange(BigDecimal revenue, BigDecimal externalRevenue) {
		if (revenue.compareTo(RSM_REVENUE_MIN) < 0 || revenue.compareTo(RSM_REVENUE_MAX) > 0
				|| externalRevenue.compareTo(RSM_REVENUE_MIN) <= 0 || externalRevenue.compareTo(RSM_REVENUE_MAX) > 0) {
			throw new BusinessException(ErrorCodeEnum.INVALID_REVENUE, RSM_REVENUE_MIN, RSM_REVENUE_MAX);
		}
	}

	private void validateRevenueRange(RSMCommissionType commissionType, BigDecimal revenue) {
		switch (commissionType) {
			case AMOUNT -> {
				if (revenue.compareTo(RSM_REVENUE_MIN) <= 0 || revenue.compareTo(RSM_REVENUE_MAX) > 0) {
					throw new BusinessException(ErrorCodeEnum.INVALID_REVENUE, RSM_REVENUE_MIN, RSM_REVENUE_MAX);
				}
			}
			case PERCENTAGE -> {
				// loongyeat: Allow 0 for revenue, as per Naresh's instructions
				// during the Biz Demo on 13/02/2025. See the meeting recording
				// at timestamp 16:10 where Angel confirms his statement.
				if (revenue.compareTo(RSM_REVENUE_MIN) < 0 || revenue.compareTo(RSM_REVENUE_MAX) > 0) {
					throw new BusinessException(ErrorCodeEnum.INVALID_REVENUE, RSM_REVENUE_MIN, RSM_REVENUE_MAX);
				}
			}
		}
	}

	@Override
	public RsmHeaderProductDTO validateProduct(RsmHeaderProductDTO request) {
		try {
			return aiFeignClient.validateProduct(request);
		}
		catch (EntityNotFoundException e) {
			throw new BusinessException(ErrorCodeEnum.INVALID_PRODUCT_GROUP_OR_TYPE);
		}
	}

	@Override
	public void validateDuplicateRsmHeader(RsmHeaderRequest request) {
		boolean exists = headerRepository.existsByCommissionTypeAndProductAndRewardName(request.getCommissionType(),
				request.getProduct().getProductCategoryId(), request.getProduct().getProductTypeId(),
				request.getProduct().getProductProviderId(), request.getProduct().getProductId(),
				request.getRewardName(), request.getRemarks());

		if (exists)
			throw new BusinessException(ErrorCodeEnum.RSM_HEADER_EXISTS);
	}

	@Override
	public void validateCompulsoryFields(RsmHeaderRequest request) {
		RsmHeaderProductDTO product = request.getProduct();
		Map<RSMFocalType, RsmHeaderDetailDTO> detail = request.getDetail();

		// Validate compulsory fields
		if (Objects.isNull(request.getIsActive()) || Objects.isNull(request.getCommissionType())
				|| StringUtils.isBlank(request.getRewardName()) || StringUtils.isBlank(request.getExternalRewardName())
				|| Objects.isNull(request.getStartDate()) || Objects.isNull(request.getEndDate())
				|| Objects.isNull(request.getExternalRevenue()) || Objects.isNull(request.getExternalStartDate())
				|| Objects.isNull(request.getExternalEndDate()) || Objects.isNull(request.getFrequency())
				|| Objects.isNull(product) || Objects.isNull(detail) || detail.isEmpty()
				|| Objects.isNull(product.getProductCategoryId()) || Objects.isNull(product.getProductCategoryName())
				|| Objects.isNull(product.getProductTypeId()) || Objects.isNull(product.getProductTypeName())
				|| Objects.isNull(product.getProductProviderId()) || Objects.isNull(product.getProductProviderName())
				|| Objects.isNull(product.getProductId()) || Objects.isNull(product.getProductName())
				|| Objects.isNull(product.getProductRevenue())) {
			throw new BusinessException(ErrorCodeEnum.REQUIRED_FIELD_EMPTY);
		}

		// Validate each revenue distribution not null
		validateAllRevenueNull(detail, RsmHeaderDetailDTO::getNullC2cRevenueDist);
		validateAllRevenueNull(detail, RsmHeaderDetailDTO::getNullB2cRevenueDist);
		validateAllRevenueNull(detail, RsmHeaderDetailDTO::getNullB2b2cRevenueDist);
		validateAllRevenueNull(detail, RsmHeaderDetailDTO::getNullNonReferralRevenueDist);

	}

	private void validateAllRevenueNull(Map<RSMFocalType, RsmHeaderDetailDTO> detail,
			Function<RsmHeaderDetailDTO, Object> getter) {
		boolean allNull = Arrays.stream(RSMFocalType.values()).allMatch(focalType -> {
			RsmHeaderDetailDTO detailDTO = detail.get(focalType);
			return detailDTO == null || getter.apply(detailDTO) == null;
		});

		if (allNull) {
			throw new BusinessException(ErrorCodeEnum.REQUIRED_FIELD_EMPTY);
		}
	}

}
