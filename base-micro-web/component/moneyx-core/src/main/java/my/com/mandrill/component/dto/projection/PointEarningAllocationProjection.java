package my.com.mandrill.component.dto.projection;

import my.com.mandrill.utilities.general.constant.SourceSystemEnum;

import java.math.BigDecimal;
import java.time.Instant;

public interface PointEarningAllocationProjection {

	String getTransactionId();

	BigDecimal getTotalPoints();

	BigDecimal getAllocatedPoints();

	BigDecimal getRemainingPoints();

	Instant getAwardedDate();

	Instant getExpiryDate();

	String getUserId();

	SourceSystemEnum getSource();

}
