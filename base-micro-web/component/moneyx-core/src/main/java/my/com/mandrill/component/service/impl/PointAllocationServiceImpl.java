package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.constant.PointTransactionType;
import my.com.mandrill.component.domain.PointAllocation;
import my.com.mandrill.component.dto.projection.PointEarningAllocationProjection;
import my.com.mandrill.component.repository.PointAllocationRepository;
import my.com.mandrill.component.service.PointAllocationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class PointAllocationServiceImpl implements PointAllocationService {

	private final PointAllocationRepository pointAllocationRepository;

	@Override
	public List<PointEarningAllocationProjection> findAvailablePointEarnings(String userId) {
		return pointAllocationRepository.findAvailablePointEarnings(userId, PointTransactionType.REFERRAL_EARNING);
	}

	@Transactional
	@Override
	public void saveAll(List<PointAllocation> pointAllocations) {
		pointAllocationRepository.saveAll(pointAllocations);
	}

	@Override
	public List<PointEarningAllocationProjection> findAllExpiredPointEarnings() {
		return pointAllocationRepository.findAllExpiredPointEarnings(PointTransactionType.REFERRAL_EARNING);
	}

}
