package my.com.mandrill.component.service;

import java.time.Instant;
import java.util.List;

public interface PointExpiryService {

    /**
     * Process expired points for a specific date range
     * This method finds all point earnings that have expired and creates expiry transactions
     * 
     * @param fromDate Start date for expiry processing (inclusive)
     * @param toDate End date for expiry processing (inclusive)
     * @return Number of expired point transactions processed
     */
    int processExpiredPoints(Instant fromDate, Instant toDate);

    /**
     * Process expired points for the current execution
     * This method processes points that expired since the last run
     * 
     * @return Number of expired point transactions processed
     */
    int processExpiredPointsForCurrentExecution();

    /**
     * Find expired point earnings that haven't been processed yet
     * 
     * @param fromDate Start date for expiry check (inclusive)
     * @param toDate End date for expiry check (inclusive)
     * @return List of expired point earning transaction IDs with remaining points
     */
    List<ExpiredPointEarning> findExpiredPointEarnings(Instant fromDate, Instant toDate);

    /**
     * DTO for expired point earnings
     */
    record ExpiredPointEarning(
        String transactionId,
        String userId,
        java.math.BigDecimal totalPoints,
        java.math.BigDecimal allocatedPoints,
        java.math.BigDecimal remainingPoints,
        Instant expiredDate,
        Instant awardedDate
    ) {}
}
