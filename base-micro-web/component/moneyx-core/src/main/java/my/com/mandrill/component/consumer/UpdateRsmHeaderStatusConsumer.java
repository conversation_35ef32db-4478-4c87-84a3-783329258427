package my.com.mandrill.component.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.dto.model.UpdateRsmHeaderStatusDTO;
import my.com.mandrill.component.service.RsmHeaderIntgService;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import my.com.mandrill.utilities.general.dto.model.SchedulerMessaging;
import my.com.mandrill.utilities.general.dto.response.UserRefereeUpdateRequest;
import my.com.mandrill.utilities.general.util.JSONUtil;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UpdateRsmHeaderStatusConsumer {

	private final JSONUtil jsonUtil;

	private final RsmHeaderIntgService rsmHeaderIntgService;

	@KafkaListener(topics = KafkaTopicConfig.SET_RSM_HEADER_STATUS, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopicConfig.SET_RSM_HEADER_STATUS)
	public void consume(String message) {
		try {
			log.info("start consume topic: {}, message: {}", KafkaTopicConfig.SET_RSM_HEADER_STATUS, message);
			UpdateRsmHeaderStatusDTO updateRsmHeaderStatusDTO = jsonUtil.convertValueFromJson(message,
					new TypeReference<>() {
					});
			rsmHeaderIntgService.UpdateRsmHeaderStatus(updateRsmHeaderStatusDTO);
		}
		catch (Exception e) {
			log.error("feature=update-rsm-header-status topic {}, message: {}, error:{}",
					KafkaTopicConfig.SET_RSM_HEADER_STATUS, message, e.getMessage(), e);
		}
	}

}
