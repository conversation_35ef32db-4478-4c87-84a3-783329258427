package my.com.mandrill.component.dto.projection;

import my.com.mandrill.component.constant.RSMFocalType;
import my.com.mandrill.utilities.general.constant.RSMRelationType;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;

import java.math.BigDecimal;
import java.time.Instant;

public record PointHistoryProjection(String id, Instant createdDate, RSMRelationType rsmScenario,
		String applicationRefNo, String commissionId, String refNo, String name, SourceSystemEnum source,
		RSMFocalType rsmDetailFocalType, String userRefNo, BigDecimal pointAmount, String accountName,
		String accountType) {
}
