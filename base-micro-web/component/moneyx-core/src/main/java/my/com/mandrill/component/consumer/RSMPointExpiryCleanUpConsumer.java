package my.com.mandrill.component.consumer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.config.KafkaTopicConfig;
import my.com.mandrill.component.service.**********************;
import my.com.mandrill.utilities.general.constant.KafkaTopic;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;

@Slf4j
@Service
@RequiredArgsConstructor
public class RSMPointExpiryCleanUpConsumer {

	private final ********************** pointExpiryIntgService;

	@KafkaListener(topics = KafkaTopic.RSM_POINT_EXPIRY_CLEAN_UP_SCHEDULER_TOPIC, groupId = KafkaTopicConfig.GROUP,
			id = KafkaTopic.RSM_POINT_EXPIRY_CLEAN_UP_SCHEDULER_TOPIC)
	public void consume(String message) {
		try {
			log.info("RSM Point Expiry Clean Up Begin {}", message);
			Instant now = Instant.now();

			pointExpiryIntgService.processExpiredPoints();

			log.info("RSM Point Expiry Clean Up Ended, Delay: {} ms", Duration.between(now, Instant.now()).toMillis());

		}
		catch (Exception e) {
			log.warn("feature=point-expiry-clean-up|error={}", e.getMessage(), e);
		}
	}

}
