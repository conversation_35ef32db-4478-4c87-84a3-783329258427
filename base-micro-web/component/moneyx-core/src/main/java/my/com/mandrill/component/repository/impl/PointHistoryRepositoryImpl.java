package my.com.mandrill.component.repository.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import my.com.mandrill.component.domain.*;
import my.com.mandrill.component.dto.projection.PointHistoryProjection;
import my.com.mandrill.component.dto.request.PointHistorySearchRequest;
import my.com.mandrill.component.repository.PointHistoryRepository;
import my.com.mandrill.utilities.core.audit.AuditSectionTimeSeries_;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Repository
public class PointHistoryRepositoryImpl implements PointHistoryRepository {

	@PersistenceContext
	private EntityManager em;

	@Override
	@Transactional(readOnly = true)
	public Page<PointHistoryProjection> findAll(Pageable pageable, PointHistorySearchRequest request) {

		CriteriaBuilder cb = em.getCriteriaBuilder();

		CriteriaQuery<PointHistoryProjection> dataCq = cb.createQuery(PointHistoryProjection.class);
		Root<PointTransaction> pt = dataCq.from(PointTransaction.class);
		Root<PointEarning> pe = dataCq.from(PointEarning.class);
		Root<RsmHeader> rh = dataCq.from(RsmHeader.class);
		Root<ChartOfAccount> coa = dataCq.from(ChartOfAccount.class);

		dataCq.select(cb.construct(PointHistoryProjection.class, pt.get(AuditSectionTimeSeries_.ID),
				pt.get(AuditSectionTimeSeries_.createdDate), pe.get(PointEarning_.rsmScenario),
				pe.get(PointEarning_.applicationRefNo), rh.get(RsmHeader_.commissionId),
				pt.get(PointTransaction_.refNo), pt.get(PointTransaction_.name), pt.get(PointTransaction_.source),
				pt.get(PointTransaction_.rsmDetailFocalType), pe.get(PointEarning_.userRefNo),
				pt.get(PointTransaction_.pointAmount), coa.get(ChartOfAccount_.accountName),
				coa.get(ChartOfAccount_.accountType)));

		dataCq.where(buildPredicates(request, cb, pt, pe, rh, coa).toArray(new Predicate[0]));

		List<PointHistoryProjection> content = em.createQuery(dataCq).setFirstResult((int) pageable.getOffset())
				.setMaxResults(pageable.getPageSize()).getResultList();

		CriteriaQuery<Long> countCq = cb.createQuery(Long.class);
		Root<PointTransaction> cPt = countCq.from(PointTransaction.class);
		Root<PointEarning> cPe = countCq.from(PointEarning.class);
		Root<RsmHeader> cRh = countCq.from(RsmHeader.class);
		Root<ChartOfAccount> cCoa = countCq.from(ChartOfAccount.class);

		countCq.select(cb.countDistinct(cPt)); // count(*) with same joins
		countCq.where(buildPredicates(request, cb, cPt, cPe, cRh, cCoa).toArray(new Predicate[0]));

		long total = em.createQuery(countCq).getSingleResult();

		return new PageImpl<>(content, pageable, total);
	}

	private List<Predicate> buildPredicates(PointHistorySearchRequest req, CriteriaBuilder cb,
			Root<PointTransaction> pt, Root<PointEarning> pe, Root<RsmHeader> rh, Root<ChartOfAccount> coa) {
		List<Predicate> predicates = new ArrayList<>();
		predicates.add(cb.equal(pt.get(PointTransaction_.refNo), pe.get(PointEarning_.refNo)));
		predicates.add(cb.equal(rh.get(AuditSectionTimeSeries_.ID),
				pe.get(PointEarning_.rsmHeader).get(AuditSectionTimeSeries_.ID)));
		predicates.add(cb.equal(coa.get(AuditSectionTimeSeries_.ID),
				pt.get(PointTransaction_.chartOfAccount).get(AuditSectionTimeSeries_.ID)));

		if (StringUtils.isNotBlank(req.getSearch())) {
			String like = "%" + req.getSearch() + "%";
			predicates.add(cb.or(cb.like(pe.get(PointEarning_.applicationRefNo), like),
					cb.like(rh.get(RsmHeader_.commissionId), like), cb.like(pt.get(PointTransaction_.refNo), like),
					cb.like(pt.get(PointTransaction_.name), like), cb.like(pe.get(PointEarning_.userRefNo), like)));
		}
		if (Objects.nonNull(req.getSource())) {
			predicates.add(cb.equal(pt.get(PointTransaction_.source), req.getSource()));
		}
		if (Objects.nonNull(req.getRsmFocalType())) {
			predicates.add(cb.equal(pt.get(PointTransaction_.rsmDetailFocalType), req.getRsmFocalType()));
		}
		if (StringUtils.isNotBlank(req.getAccountId())) {
			predicates.add(cb.equal(pt.get(PointTransaction_.chartOfAccount).get(AuditSectionTimeSeries_.ID),
					req.getAccountId()));
		}
		if (Objects.nonNull(req.getStartDate())) {
			predicates.add(cb.greaterThanOrEqualTo(pt.get(AuditSectionTimeSeries_.createdDate), req.getStartDate()));
		}
		if (Objects.nonNull(req.getEndDate())) {
			predicates.add(cb.lessThanOrEqualTo(pt.get(AuditSectionTimeSeries_.createdDate), req.getEndDate()));
		}
		return predicates;
	}

}
