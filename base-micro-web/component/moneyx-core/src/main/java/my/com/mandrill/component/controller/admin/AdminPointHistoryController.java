package my.com.mandrill.component.controller.admin;

import lombok.RequiredArgsConstructor;
import my.com.mandrill.component.dto.projection.PointHistoryProjection;
import my.com.mandrill.component.dto.request.PointHistorySearchRequest;
import my.com.mandrill.component.service.PointTransactionService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("admin")
public class AdminPointHistoryController {

	private final PointTransactionService pointTransactionService;

	@GetMapping("v1/point-histories")
	public Page<PointHistoryProjection> findAll(Pageable pageable, PointHistorySearchRequest request) {
		return pointTransactionService.findPointHistories(pageable, request);
	}

}
