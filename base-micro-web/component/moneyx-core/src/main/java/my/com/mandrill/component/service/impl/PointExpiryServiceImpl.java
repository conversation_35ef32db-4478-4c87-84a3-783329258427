package my.com.mandrill.component.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.mandrill.component.constant.ChartOfAccountType;
import my.com.mandrill.component.constant.PointTransactionStatus;
import my.com.mandrill.component.constant.PointTransactionType;
import my.com.mandrill.component.domain.ChartOfAccount;
import my.com.mandrill.component.domain.PointAllocation;
import my.com.mandrill.component.domain.PointTransaction;
import my.com.mandrill.component.repository.PointAllocationRepository;
import my.com.mandrill.component.repository.PointTransactionRepository;
import my.com.mandrill.component.service.ChartOfAccountService;
import my.com.mandrill.component.service.PointExpiryService;
import my.com.mandrill.component.service.PointTransactionService;
import my.com.mandrill.utilities.core.util.RunningNumberUtil;
import my.com.mandrill.utilities.general.constant.RunningNumberModule;
import my.com.mandrill.utilities.general.constant.SourceSystemEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class PointExpiryServiceImpl implements PointExpiryService {

    private final PointTransactionRepository pointTransactionRepository;
    private final PointAllocationRepository pointAllocationRepository;
    private final PointTransactionService pointTransactionService;
    private final ChartOfAccountService chartOfAccountService;
    private final RunningNumberUtil runningNumberUtil;

    @Override
    @Transactional
    public int processExpiredPoints(Instant fromDate, Instant toDate) {
        log.info("feature=point-expiry|action=process-expired-points|from={}|to={}", fromDate, toDate);
        
        List<ExpiredPointEarning> expiredEarnings = findExpiredPointEarnings(fromDate, toDate);
        
        if (expiredEarnings.isEmpty()) {
            log.info("feature=point-expiry|message=no expired points found for processing");
            return 0;
        }
        
        log.info("feature=point-expiry|message=found {} expired point earnings to process", expiredEarnings.size());
        
        ChartOfAccount expiryChartOfAccount = chartOfAccountService
                .findByAccountCode(ChartOfAccountType.RSM_POINT_EXPIRY.getCode());
        
        List<PointTransaction> expiryTransactions = new ArrayList<>();
        List<PointAllocation> allocations = new ArrayList<>();
        
        for (ExpiredPointEarning expiredEarning : expiredEarnings) {
            // Create expiry transaction
            PointTransaction expiryTransaction = createExpiryTransaction(
                expiredEarning, 
                expiryChartOfAccount
            );
            expiryTransactions.add(expiryTransaction);
            
            // Create allocation record linking the expired earning to the expiry transaction
            PointAllocation allocation = createExpiryAllocation(
                expiredEarning.transactionId(), 
                expiryTransaction.getId(), 
                expiredEarning.remainingPoints()
            );
            allocations.add(allocation);
        }
        
        // Save all transactions and allocations
        pointTransactionService.saveAll(expiryTransactions);
        pointAllocationRepository.saveAll(allocations);
        
        BigDecimal totalExpiredPoints = expiredEarnings.stream()
                .map(ExpiredPointEarning::remainingPoints)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        log.info("feature=point-expiry|message=processed {} expired point transactions|total-expired-points={}", 
                expiredEarnings.size(), totalExpiredPoints);
        
        return expiredEarnings.size();
    }

    @Override
    @Transactional
    public int processExpiredPointsForCurrentExecution() {
        // Process points that expired up to now
        Instant now = Instant.now();
        
        // For safety, we process points that expired up to yesterday to avoid timezone issues
        Instant processingCutoff = now.minusSeconds(24 * 60 * 60); // 24 hours ago
        
        // We don't need a specific "from" date since we're looking for any expired points
        // that haven't been processed yet (those without expiry allocations)
        return processExpiredPoints(Instant.EPOCH, processingCutoff);
    }

    @Override
    public List<ExpiredPointEarning> findExpiredPointEarnings(Instant fromDate, Instant toDate) {
        // Native query to find expired point earnings with remaining points
        String query = """
            SELECT 
                pt.id as transactionId,
                pt.user_id as userId,
                pt.point_amount as totalPoints,
                COALESCE(SUM(pa.points_allocated), 0) as allocatedPoints,
                (pt.point_amount - COALESCE(SUM(pa.points_allocated), 0)) as remainingPoints,
                pt.expired_due as expiredDate,
                pt.approved_date as awardedDate
            FROM point_transaction pt
            LEFT JOIN point_allocation pa ON pt.id = pa.origin_tx_id
            WHERE pt.type = 'REFERRAL_EARNING'
              AND pt.expired_due IS NOT NULL
              AND pt.expired_due BETWEEN :fromDate AND :toDate
              AND pt.user_id IS NOT NULL
            GROUP BY pt.id, pt.user_id, pt.point_amount, pt.expired_due, pt.approved_date
            HAVING (pt.point_amount - COALESCE(SUM(pa.points_allocated), 0)) > 0
            ORDER BY pt.expired_due ASC, pt.approved_date ASC
        """;
        
        // Execute the native query and map results
        return pointTransactionRepository.findExpiredPointEarnings(fromDate, toDate);
    }

    private PointTransaction createExpiryTransaction(ExpiredPointEarning expiredEarning, ChartOfAccount chartOfAccount) {
        PointTransaction expiryTransaction = new PointTransaction();
        expiryTransaction.setRefNo(runningNumberUtil.getLatestRunningNumber(RunningNumberModule.TRX_ID.name()));
        expiryTransaction.setUserId(expiredEarning.userId());
        expiryTransaction.setStatus(PointTransactionStatus.SUCCESS);
        expiryTransaction.setPointAmount(expiredEarning.remainingPoints());
        expiryTransaction.setSource(SourceSystemEnum.MXAPP); // System-generated expiry
        expiryTransaction.setApprovedDate(Instant.now());
        expiryTransaction.setType(PointTransactionType.POINT_EXPIRY);
        expiryTransaction.setName(PointTransactionType.POINT_EXPIRY.getName());
        expiryTransaction.setChartOfAccount(chartOfAccount);
        // expiredDue is null for expiry transactions as they don't expire themselves
        
        return expiryTransaction;
    }

    private PointAllocation createExpiryAllocation(String originTxId, String expiryTxId, BigDecimal pointsAllocated) {
        PointAllocation allocation = new PointAllocation();
        allocation.setOriginTxId(originTxId);
        allocation.setDestTxId(expiryTxId); // For expiry, we use the expiry transaction ID
        allocation.setPointsAllocated(pointsAllocated);
        
        return allocation;
    }
}
