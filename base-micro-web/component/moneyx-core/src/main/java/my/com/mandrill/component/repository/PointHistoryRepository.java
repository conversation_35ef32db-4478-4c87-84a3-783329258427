package my.com.mandrill.component.repository;

import my.com.mandrill.component.dto.projection.PointHistoryProjection;
import my.com.mandrill.component.dto.request.PointHistorySearchRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface PointHistoryRepository {

	Page<PointHistoryProjection> findAll(Pageable p, PointHistorySearchRequest request);

}
