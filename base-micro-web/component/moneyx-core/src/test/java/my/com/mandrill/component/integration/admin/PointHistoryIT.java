package my.com.mandrill.component.integration.admin;

import my.com.mandrill.component.integration.extension.BaseIntegrationTest;
import my.com.mandrill.component.repository.PointEarningRepository;
import my.com.mandrill.component.repository.PointTransactionRepository;
import my.com.mandrill.component.repository.RsmDetailRepository;
import my.com.mandrill.component.repository.RsmHeaderRepository;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

class PointHistoryIT extends BaseIntegrationTest {

	@Autowired
	private MockMvc mockMvc;

	@Autowired
	private RsmHeaderRepository rsmHeaderRepository;

	@Autowired
	private PointTransactionRepository pointTransactionRepository;

	@Autowired
	private PointEarningRepository pointEarningRepository;

	@Autowired
	private RsmDetailRepository rsmDetailRepository;

	@AfterEach
	public void tearDown() {
		pointTransactionRepository.deleteAll();
		pointEarningRepository.deleteAll();
		rsmDetailRepository.deleteAll();
		rsmHeaderRepository.deleteAll();
	}

	@Test
	void testFindAllSuccess() throws Exception {
		String expect = getExpectationString();
		mockMvc.perform(get("/admin/v1/point-histories").header("Authorization", "Bearer " + getAdminSecretToken()))
				.andExpect(MockMvcResultMatchers.status().isOk())
				.andExpect(MockMvcResultMatchers.content().json(expect, false));
	}

}
