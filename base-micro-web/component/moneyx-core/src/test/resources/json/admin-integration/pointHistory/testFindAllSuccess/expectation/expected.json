{"content": [{"id": "01JPP6PVSS635BN242VQ4N7KXQ", "createdDate": "2025-03-19T03:21:16.857850Z", "rsmScenario": "C2C", "applicationRefNo": "****************", "commissionId": "RSM0000000026", "refNo": "REMX2025030000001370", "name": "Referral Earnings", "source": "MXAPP", "rsmDetailFocalType": "REFERRER_LEVEL_1", "userRefNo": "**************", "pointAmount": 35000.0, "accountName": "RSM Point Earn", "accountType": "Revenue"}, {"id": "01JPP6PVSRAWCN561PR5NH7WYV", "createdDate": "2025-03-19T03:21:16.856501Z", "rsmScenario": "C2C", "applicationRefNo": "****************", "commissionId": "RSM0000000026", "refNo": "REMX2025030000001370", "name": "Expense Points to MX User", "source": "MXAPP", "rsmDetailFocalType": "MONEYX", "userRefNo": "**************", "pointAmount": 35000.0, "accountName": "RSM Point Primary Expense", "accountType": "Expense"}], "pageable": {"sort": [], "offset": 0, "pageSize": 20, "pageNumber": 0, "paged": true, "unpaged": false}, "last": true, "totalPages": 1, "totalElements": 2, "first": true, "size": 20, "number": 0, "sort": [], "numberOfElements": 2, "empty": false}