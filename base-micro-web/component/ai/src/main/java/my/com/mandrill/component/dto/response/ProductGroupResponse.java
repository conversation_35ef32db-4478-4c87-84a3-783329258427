package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductGroupResponse implements Serializable {

	private String id;

	private String value;

	private String code;

	private List<ProductGroupResponse> subMenus;

}
