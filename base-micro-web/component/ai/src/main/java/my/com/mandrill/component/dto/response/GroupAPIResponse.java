package my.com.mandrill.component.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupAPIResponse implements Serializable {

	private List<ProductGroupResponse> productGroups;

	private List<ProductTypeResponse> productTypes;

}
