package my.com.mandrill.component.dto.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import my.com.mandrill.utilities.general.dto.HttpDetailDTO;

import java.util.Optional;

@Data
@Builder
@AllArgsConstructor
public class CaptchaVerifyData {

	private String token;

	private String action;

	private HttpDetailDTO httpDetail;

	public HttpDetailDTO getHttpDetail() {
		return Optional.ofNullable(httpDetail).orElse(new HttpDetailDTO());
	}

}
