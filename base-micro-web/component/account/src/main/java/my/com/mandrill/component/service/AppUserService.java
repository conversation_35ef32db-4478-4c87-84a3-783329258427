package my.com.mandrill.component.service;

import my.com.mandrill.component.domain.AppUser;
import my.com.mandrill.utilities.general.constant.LoginTypeEnum;

import java.util.Optional;

public interface AppUserService {

	AppUser findByRefNoAndLoginType(String refNo, LoginTypeEnum loginType);

	AppUser findByRefNo(String refNo);

	AppUser findAuthByPhoneNumberAndPhoneCountryAndLoginTypeAndDeletedFalse(String phoneNumber, String phoneCountry,
			LoginTypeEnum loginType);

	Optional<AppUser> findSafeByPhoneNumberAndPhoneCountryAndLoginTypeAndDeletedFalse(String phoneNumber,
			String phoneCountry, LoginTypeEnum loginType);

	AppUser findAuthByEmailAndActiveAndLoginType(String email, LoginTypeEnum loginType);

	AppUser findByEmailAndActiveAndLoginTypeAndDeletedDateNull(String email, LoginTypeEnum loginType);

	Optional<AppUser> findSafeUserByEmailAndActiveAndLoginType(String email, LoginTypeEnum loginType);

	AppUser save(AppUser appUser);

	AppUser findByEmailOrPhoneNumber(String refOrPhone, LoginTypeEnum loginType);

	void processFailedLogin(AppUser appUser);

	void processGracePeriod(AppUser appUser);

	void resetFailedLogin(AppUser userId);

	Optional<AppUser> findById(String id);

}
